package cron

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/go-co-op/gocron/v2"
	"github.com/sayeworldevelopment/wp-core/pkg/config"
	"github.com/sayeworldevelopment/wp-core/pkg/database"
	"github.com/sayeworldevelopment/wp-core/pkg/dtos"
	"github.com/sayeworldevelopment/wp-core/pkg/entities"
	sayelog "github.com/sayeworldevelopment/wp-core/pkg/log"
	"github.com/sayeworldevelopment/wp-core/pkg/utils"
	"github.com/sayeworldevelopment/wp-core/pkg/wrapper"
	"go.mau.fi/whatsmeow/types"
)

func MyCron() {
	s, _ := gocron.NewScheduler()

	// Mevcut zone control job'ı
	j1, _ := s.NewJob(
		gocron.CronJob(
			`0 0 23 * * *`,
			false,
		),
		gocron.NewTask(
			zoneControl,
		),
	)
	log.Println("Zone control job ID:", j1.ID())

	// 10 dakikada bir tüm clientları yeniden başlat
	j2, _ := s.NewJob(
		gocron.CronJob(
			`0 */5 * * * *`, // Her 10 dakikada bir
			false,
		),
		gocron.NewTask(
			reconnectAllClients,
		),
	)
	log.Println("Reconnect all clients job ID:", j2.ID())

	// 1 dakikadan fazla online olanları kontrol et ve yeniden başlat
	j3, _ := s.NewJob(
		gocron.CronJob(
			`0 */2 * * * *`, // Her 5 dakikada bir kontrol et
			false,
		),
		gocron.NewTask(
			checkLongOnlinePresences,
		),
	)
	log.Println("Check long online presences job ID:", j3.ID())

	s.Start()
	log.Println("MyCron started with all jobs...")
}

const (
	brightdata_url = "https://api.brightdata.com/%s"
)

func zoneControl() {
	log.Println("ZoneControl started...")

	var (
		resp_for_status dtos.ResponseForStatus
	)

	headers := map[string]string{
		"Authorization": config.InitConfig().BrightData.ApiKey,
	}

	if err := utils.Get(nil, fmt.Sprintf(brightdata_url, "status"), resp_for_status, headers); err != nil {
		return
	}

	log.Println("BrightData status:", resp_for_status.Status)
}

// reconnectAllClients - 10 dakikada bir tüm clientları yeniden başlatır
func reconnectAllClients() {
	log.Println("ReconnectAllClients started...")
	ctx := context.Background()
	db := database.DBClient()
	w := wrapper.WpClient()

	sayelog.CreateLog(&entities.Log{
		Title:   "Reconnect All Clients Started",
		Message: "Starting scheduled reconnection of all clients",
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	// Tüm aktif sessionları çek
	var sessions []entities.Session
	err := db.WithContext(ctx).Where("status = ?", entities.SessionStatusConnected).Find(&sessions).Error
	if err != nil {
		sayelog.CreateLog(&entities.Log{
			Title:   "Reconnect All Clients Failed",
			Message: "Failed to get active sessions: " + err.Error(),
			Entity:  "system",
			Type:    "error",
			Ip:      "system",
		})
		return
	}

	log.Printf("Found %d active sessions to reconnect", len(sessions))

	for _, session := range sessions {
		log.Printf("Reconnecting session: %s (JID: %s)", session.ID, session.JID)

		// Mevcut client'ı kapat ve yeniden başlat
		client, isConnected := w.CheckDevice(ctx, session.JID, session.RegID)
		if !isConnected {
			sayelog.CreateLog(&entities.Log{
				Title:   "Reconnect Failed",
				Message: fmt.Sprintf("Failed to reconnect session %s", session.ID),
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})
			continue
		}

		// Bu session'ın tüm aktif subscription'larını çek ve yeniden başlat
		var subscriptions []entities.SessionSubscription
		err := db.Where("session_id = ? AND active = ?", session.ID, true).Find(&subscriptions).Error
		if err != nil {
			sayelog.CreateLog(&entities.Log{
				Title:   "Reconnect Subscription Failed",
				Message: fmt.Sprintf("Failed to get subscriptions for session %s: %s", session.ID, err.Error()),
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})
			continue
		}

		// Her subscription için yeniden subscribe et
		for _, sub := range subscriptions {
			// Eski event handler'ı kaldır
			if sub.EventHandlerId != 0 {
				client.RemoveEventHandler(sub.EventHandlerId)
			}

			// Yeni event handler ekle
			newEventHandlerId := client.AddEventHandler(wrapper.EventHandler)

			// Database'de event handler ID'yi güncelle
			db.Model(&entities.SessionSubscription{}).
				Where("id = ?", sub.ID).
				Update("event_handler_id", newEventHandlerId)

			// Presence'ı subscribe et
			subJid := sub.Phone + "@s.whatsapp.net"
			parsedJID, _ := types.ParseJID(subJid)
			client.SendPresence(types.PresenceAvailable)

			err = client.SubscribePresence(parsedJID)
			if err != nil {
				sayelog.CreateLog(&entities.Log{
					Title:   "Reconnect Subscribe Failed",
					Message: fmt.Sprintf("Failed to subscribe to %s for session %s: %s", sub.Phone, session.ID, err.Error()),
					Entity:  "system",
					Type:    "error",
					Ip:      "system",
				})
				continue
			}

			log.Printf("Successfully renewed subscription for phone: %s", sub.Phone)
		}

		sayelog.CreateLog(&entities.Log{
			Title:   "Session Reconnected",
			Message: fmt.Sprintf("Successfully reconnected session %s with %d subscriptions", session.ID, len(subscriptions)),
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})
	}

	sayelog.CreateLog(&entities.Log{
		Title:   "Reconnect All Clients Completed",
		Message: fmt.Sprintf("Completed reconnection of %d sessions", len(sessions)),
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	log.Println("ReconnectAllClients completed")
}

// checkLongOnlinePresences - 1 dakikadan fazla online olan presence kayıtlarını kontrol eder ve clientları yeniden başlatır
func checkLongOnlinePresences() {
	log.Println("CheckLongOnlinePresences started...")
	ctx := context.Background()
	db := database.DBClient()
	w := wrapper.WpClient()

	sayelog.CreateLog(&entities.Log{
		Title:   "Check Long Online Presences Started",
		Message: "Starting check for long online presences",
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	// 1 dakikadan fazla online olan presence kayıtlarını bul
	// Son online kaydından itibaren 1 dakika geçmiş ve hala offline kaydı olmayan durumları bul
	oneMinuteAgo := time.Now().Add(-1 * time.Minute)

	var longOnlinePresences []entities.Presence
	err := db.WithContext(ctx).
		Where("status = ? AND created_at < ?", "online", oneMinuteAgo).
		Find(&longOnlinePresences).Error

	if err != nil {
		sayelog.CreateLog(&entities.Log{
			Title:   "Check Long Online Presences Failed",
			Message: "Failed to get long online presences: " + err.Error(),
			Entity:  "system",
			Type:    "error",
			Ip:      "system",
		})
		return
	}

	if len(longOnlinePresences) == 0 {
		log.Println("No long online presences found")
		return
	}

	log.Printf("Found %d long online presences to check", len(longOnlinePresences))

	// Session ID'leri topla (unique)
	sessionMap := make(map[string]bool)
	for _, presence := range longOnlinePresences {
		// Her presence için, o telefon numarasının son kaydına bak
		var latestPresence entities.Presence
		err := db.WithContext(ctx).
			Where("session_id = ? AND subscribe_phone = ?", presence.SessionId, presence.SubscribePhone).
			Order("created_at DESC").
			First(&latestPresence).Error

		if err != nil {
			continue
		}

		// Eğer son kayıt online ise ve 1 dakikadan eski ise, bu session'ı yeniden başlat
		if latestPresence.Status == "online" && latestPresence.CreatedAt.Before(oneMinuteAgo) {
			sessionMap[presence.SessionId] = true
		}
	}

	if len(sessionMap) == 0 {
		log.Println("No sessions need reconnection for long online presences")
		return
	}

	log.Printf("Found %d sessions to reconnect due to long online presences", len(sessionMap))

	// Her unique session için yeniden bağlantı yap
	for sessionIdStr := range sessionMap {
		var session entities.Session
		err := db.WithContext(ctx).Where("id = ?", sessionIdStr).First(&session).Error
		if err != nil {
			sayelog.CreateLog(&entities.Log{
				Title:   "Long Online Check Failed",
				Message: fmt.Sprintf("Failed to get session %s: %s", sessionIdStr, err.Error()),
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})
			continue
		}

		log.Printf("Reconnecting session due to long online presence: %s (JID: %s)", session.ID, session.JID)

		// Client'ı yeniden başlat
		client, isConnected := w.CheckDevice(ctx, session.JID, session.RegID)
		if !isConnected {
			sayelog.CreateLog(&entities.Log{
				Title:   "Long Online Reconnect Failed",
				Message: fmt.Sprintf("Failed to reconnect session %s due to long online presence", session.ID),
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})
			continue
		}

		// Bu session'ın tüm aktif subscription'larını çek ve yeniden başlat
		var subscriptions []entities.SessionSubscription
		err = db.Where("session_id = ? AND active = ?", session.ID, true).Find(&subscriptions).Error
		if err != nil {
			sayelog.CreateLog(&entities.Log{
				Title:   "Long Online Subscription Failed",
				Message: fmt.Sprintf("Failed to get subscriptions for session %s: %s", session.ID, err.Error()),
				Entity:  "system",
				Type:    "error",
				Ip:      "system",
			})
			continue
		}

		// Her subscription için yeniden subscribe et
		for _, sub := range subscriptions {
			// Eski event handler'ı kaldır
			if sub.EventHandlerId != 0 {
				client.RemoveEventHandler(sub.EventHandlerId)
			}

			// Yeni event handler ekle
			newEventHandlerId := client.AddEventHandler(wrapper.EventHandler)

			// Database'de event handler ID'yi güncelle
			db.Model(&entities.SessionSubscription{}).
				Where("id = ?", sub.ID).
				Update("event_handler_id", newEventHandlerId)

			// Presence'ı subscribe et
			subJid := sub.Phone + "@s.whatsapp.net"
			parsedJID, _ := types.ParseJID(subJid)
			client.SendPresence(types.PresenceAvailable)

			err = client.SubscribePresence(parsedJID)
			if err != nil {
				sayelog.CreateLog(&entities.Log{
					Title:   "Long Online Subscribe Failed",
					Message: fmt.Sprintf("Failed to subscribe to %s for session %s: %s", sub.Phone, session.ID, err.Error()),
					Entity:  "system",
					Type:    "error",
					Ip:      "system",
				})
				continue
			}

			log.Printf("Successfully renewed subscription for phone: %s (long online check)", sub.Phone)
		}

		sayelog.CreateLog(&entities.Log{
			Title:   "Long Online Session Reconnected",
			Message: fmt.Sprintf("Successfully reconnected session %s due to long online presence with %d subscriptions", session.ID, len(subscriptions)),
			Entity:  "system",
			Type:    "info",
			Ip:      "system",
		})
	}

	sayelog.CreateLog(&entities.Log{
		Title:   "Check Long Online Presences Completed",
		Message: fmt.Sprintf("Completed check and reconnected %d sessions due to long online presences", len(sessionMap)),
		Entity:  "system",
		Type:    "info",
		Ip:      "system",
	})

	log.Println("CheckLongOnlinePresences completed")
}
